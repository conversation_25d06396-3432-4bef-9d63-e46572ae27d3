<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="../../build/dotnet-version.props" />
	<PropertyGroup>
		<TargetFrameworks>$(ExtraTargetFrameworks)netstandard2.0</TargetFrameworks>
		<LangVersion>9.0</LangVersion>
		<!--
		<Nullable>Enable</Nullable>
		-->
	</PropertyGroup>
	<Import Project="../../build/Avalonia.props" />
	<ItemGroup>
		<PackageReference Include="Avalonia.Themes.Fluent" Version="$(AvaloniaVer)" />
	</ItemGroup>
	
	<ItemGroup>
		<AvaloniaResource Include="AVMAP/*.axaml" />

		<AvaloniaResource Include="Fonts/**" />
		<AvaloniaResource Include="Fonts2/**" />
		<AvaloniaResource Include="Resources/**" />
	</ItemGroup>
</Project>
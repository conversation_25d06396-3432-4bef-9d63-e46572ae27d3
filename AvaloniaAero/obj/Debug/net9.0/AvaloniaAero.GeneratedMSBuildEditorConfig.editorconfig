is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = AvaloniaAero
build_property.ProjectDir = D:\projects\_csharp\STAGALauncher\AvaloniaAero\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/AeroTheme.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/AVMAP/Brushes.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/AVMAP/Common.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/AVMAP/Controls.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/AVMAP/Metrics.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/AVMAP/StyleTrailMix.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/ButtonSpinner.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/Button_RepeatButton_ToggleButton.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/CheckBox.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/HeaderedContentControl.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/Menu.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/NumericUpDown.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/ProgressBar.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/RadioButton.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/ScrollBar.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/ScrollViewer.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/TabControl.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/TabItem.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/TextBox.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/ToggleSwitch.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/ControlStyles/Window.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/Variant/Dark.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/projects/_csharp/STAGALauncher/AvaloniaAero/Variant/Light.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

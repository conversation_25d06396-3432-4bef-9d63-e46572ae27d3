<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:aecnv="using:AvaloniaAero.Converters"
                    x:ClassModifier="internal">
    <ControlTheme x:Key="AeroScrollBarThumbBase" TargetType="Thumb">
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="#01000000">
                    <Border x:Name="Outer"
                            Classes="aeroState2"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Border x:Name="Inner"
                                Classes="aeroState2"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{DynamicResource AeroButtonIdleInnerBorderBrush}"
                                BorderThickness="1">
                            <StackPanel Classes="gripLines" Spacing="-1">
                                <Rectangle/>
                                <Rectangle/>
                                <Rectangle/>
                            </StackPanel>
                        </Border>
                    </Border>
                </Border>
            </ControlTemplate>
        </Setter>
        
        <Style Selector="^ /template/ Border#Outer">
            <Setter Property="CornerRadius" Value="3"/>
        </Style>
        <Style Selector="^ /template/ Border#Inner">
            <Setter Property="CornerRadius" Value="2"/>
        </Style>

        
        <Style Selector="^ /template/ StackPanel.gripLines">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Opacity" Duration="{StaticResource AeroScrollExpandDuration}" />
                </Transitions>
            </Setter>
            <Setter Property="Opacity" Value="0.5" />
        </Style>
        <Style Selector="^.scrollBarHideLines /template/ StackPanel.gripLines">
            <Setter Property="Opacity" Value="0" />
        </Style>

        <Style Selector="^ /template/ StackPanel.gripLines > Rectangle">
            <Setter Property="Fill" Value="{DynamicResource AeroScrollBarButtonForeground}" />
            <Setter Property="Stroke" Value="{DynamicResource AeroScrollBarThumbIconStroke}" />
            <Setter Property="StrokeThickness" Value="1" />
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="AeroScrollBarThumbVertical" TargetType="Thumb" BasedOn="{StaticResource AeroScrollBarThumbBase}">
        <Setter Property="MinHeight" Value="{DynamicResource AeroScrollBarThumbMinimumExtent}"/>
        <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonIdleBackground}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonIdleOuterBorderBrush}"/>

        <Style Selector="^ /template/ Border#Outer">
            <Setter Property="Margin" Value="1,0"/>
        </Style>

        <Style Selector="^ /template/ StackPanel.gripLines">
            <Setter Property="Orientation" Value="Vertical" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="1,0" />
        </Style>
        <Style Selector="^ /template/ StackPanel.gripLines > Rectangle">
            <Setter Property="Height" Value="4" />
        </Style>


        <!-- pointerover (hover) state -->
        <Style Selector="^:pointerover /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonHoverOuterBorderBrush}"/>
        </Style>
        <Style Selector="^:pointerover /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonHoverBackground}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonHoverInnerBorderBrush}"/>
        </Style>


        <!-- pressed state -->
        <Style Selector="^:pressed /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonPressedOuterBorderBrush}"/>
        </Style>
        <Style Selector="^:pressed /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonPressedBackground}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonPressedInnerBorderBrush}"/>
        </Style>


        <!-- disabled state -->
        <Style Selector="^:disabled /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonDisabledOuterBorderBrush}"/>
        </Style>
        <Style Selector="^:disabled /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonDisabledBackground}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonDisabledInnerBorderBrush}"/>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="AeroScrollBarThumbHorizontal" TargetType="Thumb" BasedOn="{StaticResource AeroScrollBarThumbBase}">
        <Setter Property="MinWidth" Value="{DynamicResource AeroScrollBarThumbMinimumExtent}"/>
        <Setter Property="Background" Value="{DynamicResource AeroButtonIdleBackground}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AeroButtonIdleOuterBorderBrush}"/>

        <Style Selector="^ /template/ Border#Outer">
            <Setter Property="Margin" Value="0,1"/>
        </Style>

        <Style Selector="^ /template/ StackPanel.gripLines">
            <Setter Property="Orientation" Value="Horizontal" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Stretch" />
        </Style>
        <Style Selector="^ /template/ StackPanel.gripLines > Rectangle">
            <Setter Property="Width" Value="4" />
            <Setter Property="Margin" Value="0,1" />
        </Style>


        <!-- pointerover (hover) state -->
        <Style Selector="^:pointerover /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroButtonHoverOuterBorderBrush}" />
        </Style>
        <Style Selector="^:pointerover /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroButtonHoverBackground}" />
            <Setter Property="BorderBrush" Value="{DynamicResource AeroButtonHoverInnerBorderBrush}" />
        </Style>


        <!-- pressed state -->
        <Style Selector="^:pressed /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroButtonPressedOuterBorderBrush}" />
        </Style>
        <Style Selector="^:pressed /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroButtonPressedBackground}" />
            <Setter Property="BorderBrush" Value="{DynamicResource AeroButtonPressedInnerBorderBrush}" />
        </Style>


        <!-- disabled state -->
        <Style Selector="^:disabled /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroButtonDisabledOuterBorderBrush}" />
        </Style>
        <Style Selector="^:disabled /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroButtonDisabledBackground}" />
            <Setter Property="BorderBrush" Value="{DynamicResource AeroButtonDisabledInnerBorderBrush}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="TextBlock.Foreground" Value="{DynamicResource AeroButtonDisabledForeground}"/>
        </Style>
    </ControlTheme>
    
    
    <ControlTheme x:Key="AeroScrollBarPageButton" TargetType="RepeatButton">
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="#01000000" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>


    <ControlTheme x:Key="AeroScrollBarLineButtonBase" TargetType="RepeatButton" BasedOn="{StaticResource {x:Type RepeatButton}}">
        <Setter Property="Transitions">
            <Transitions>
                <DoubleTransition Property="Width" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                <DoubleTransition Property="Height" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                <DoubleTransition Property="Opacity" Duration="{StaticResource AeroScrollExpandDuration}" />
            </Transitions>
        </Setter>
        
        <Style Selector="^ /template/ Panel">
            <Setter Property="Background" Value="#01000000" />
        </Style>

        <Style Selector="^:not(.scrollBarHideIdle) /template/ Border#Outer">
            <Style.Animations>
                <Animation FillMode="Both" Duration="{StaticResource AeroScrollExpandDuration}">
                    <KeyFrame Cue="100%">
                        <Setter Property="Opacity" Value="1.0"/>
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
        <Style Selector="^.scrollBarHideIdle /template/ Border#Outer">
            <Style.Animations>
                <Animation FillMode="Both" Duration="{StaticResource AeroScrollExpandDuration}">
                    <KeyFrame Cue="100%">
                        <Setter Property="Opacity" Value="0.0"/>
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="AeroScrollBarLineButtonHorizontal" TargetType="RepeatButton" BasedOn="{StaticResource AeroScrollBarLineButtonBase}">
        <Setter Property="Width" Value="{DynamicResource AeroScrollBarLineButtonExtent}" />
        <Setter Property="VerticalAlignment" Value="Stretch"/>

        <Style Selector="^ /template/ Border#Outer">
            <Setter Property="Margin" Value="0,1"/>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="AeroScrollBarLineButtonVertical" TargetType="RepeatButton" BasedOn="{StaticResource AeroScrollBarLineButtonBase}">
        <Setter Property="Height" Value="{DynamicResource AeroScrollBarLineButtonExtent}" />
        <Setter Property="HorizontalAlignment" Value="Stretch"/>

        <Style Selector="^ /template/ Border#Outer">
            <Setter Property="Margin" Value="1,0"/>
        </Style>

        <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonIdleBackground}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonIdleOuterBorderBrush}"/>


        <!-- pointerover (hover) state -->
        <Style Selector="^:pointerover /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonHoverOuterBorderBrush}"/>
        </Style>
        <Style Selector="^:pointerover /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonHoverBackground}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonHoverInnerBorderBrush}"/>
        </Style>


        <!-- pressed state -->
        <Style Selector="^:pressed /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonPressedOuterBorderBrush}"/>
        </Style>
        <Style Selector="^:pressed /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonPressedBackground}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonPressedInnerBorderBrush}"/>
        </Style>


        <!-- disabled state -->
        <Style Selector="^:disabled /template/ Border#Outer">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonDisabledOuterBorderBrush}"/>
        </Style>
        <Style Selector="^:disabled /template/ Border#Inner">
            <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarButtonDisabledBackground}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarButtonDisabledInnerBorderBrush}"/>
        </Style>
    </ControlTheme>




    
    <ControlTheme x:Key="{x:Type ScrollBar}" TargetType="ScrollBar">
        <Setter Property="MinWidth" Value="{DynamicResource AeroScrollBarThickness}"/>
        <Setter Property="MinHeight" Value="{DynamicResource AeroScrollBarThickness}"/>

        <Style Selector="^:vertical">
            <Setter Property="Background" Value="{DynamicResource AeroVerticalScrollBarTrackBackground}" />
            <Setter Property="BorderBrush" Value="{DynamicResource AeroVerticalScrollBarTrackBorderBrush}" />
            <Setter Property="BorderThickness" Value="1,0" />
            <Setter Property="Template">
                <ControlTemplate>
                    <Border Background="#01000000">
                        <Border Classes="root">
                            <Grid RowDefinitions="Auto,*,Auto">
                                <Border Classes="expandedBackground"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Grid.RowSpan="3"/>


                                <RepeatButton x:Name="PART_LineUpButton"
                                                Theme="{StaticResource AeroScrollBarLineButtonVertical}"
                                                Classes="lineButton"
                                                Classes.scrollBarHideIdle="{Binding !IsPointerOver, RelativeSource={RelativeSource Mode=TemplatedParent}, Mode=OneWay}"
                                                Grid.Row="0"
                                                Focusable="False">
                                    <Path Data="M 0 4 L 8 4 L 4 0 Z" />
                                </RepeatButton>

                                <Track Grid.Row="1"
                                        Minimum="{TemplateBinding Minimum}"
                                        Maximum="{TemplateBinding Maximum}"
                                        Value="{TemplateBinding Value, Mode=TwoWay}"
                                        ViewportSize="{TemplateBinding ViewportSize}"
                                        Orientation="{TemplateBinding Orientation}"
                                        IsDirectionReversed="True">
                                    <Track.DecreaseButton>
                                        <RepeatButton x:Name="PART_PageUpButton"
                                                        Theme="{StaticResource AeroScrollBarPageButton}"
                                                        Focusable="False"/>
                                    </Track.DecreaseButton>
                                    <Track.IncreaseButton>
                                        <RepeatButton x:Name="PART_PageDownButton"
                                                        Theme="{StaticResource AeroScrollBarPageButton}"
                                                        Focusable="False"/>
                                    </Track.IncreaseButton>
                                    <Thumb Theme="{StaticResource AeroScrollBarThumbVertical}"
                                        Classes.scrollBarHideLines="{Binding !IsExpanded, RelativeSource={RelativeSource Mode=TemplatedParent}, Mode=OneWay}" />
                                </Track>

                                <RepeatButton x:Name="PART_LineDownButton"
                                                Theme="{StaticResource AeroScrollBarLineButtonVertical}"
                                                Classes="lineButton"
                                                Classes.scrollBarHideIdle="{Binding !IsPointerOver, RelativeSource={RelativeSource Mode=TemplatedParent}, Mode=OneWay}"
                                                Grid.Row="2"
                                                Focusable="False">
                                    <Path Data="M 0 0 L 4 4 L 8 0 Z" />
                                </RepeatButton>
                            </Grid>
                        </Border>
                    </Border>
                </ControlTemplate>
            </Setter>
        </Style>
        <Style Selector="^:horizontal">
            <Setter Property="Background" Value="{DynamicResource AeroHorizontalScrollBarTrackBackground}" />
            <Setter Property="BorderBrush" Value="{DynamicResource AeroHorizontalScrollBarTrackBorderBrush}" />
            <Setter Property="BorderThickness" Value="0,1" />
            <Setter Property="Template">
                <ControlTemplate>
                    <Border Background="#01000000">
                        <Border Classes="root">
                            <Grid ColumnDefinitions="Auto,*,Auto">
                                <Border Classes="expandedBackground"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Grid.ColumnSpan="3"/>


                                <RepeatButton x:Name="PART_LineUpButton"
                                                Theme="{StaticResource AeroScrollBarLineButtonHorizontal}"
                                                Classes="lineButton"
                                                Classes.scrollBarHideIdle="{Binding !IsPointerOver, RelativeSource={RelativeSource Mode=TemplatedParent}, Mode=OneWay}"
                                                Grid.Column="0"
                                                Focusable="False">
                                    <Path Data="M 4 0 L 4 8 L 0 4 Z" />
                                </RepeatButton>

                                <Track Grid.Row="1"
                                        Grid.Column="1"
                                        Minimum="{TemplateBinding Minimum}"
                                        Maximum="{TemplateBinding Maximum}"
                                        Value="{TemplateBinding Value, Mode=TwoWay}"
                                        ViewportSize="{TemplateBinding ViewportSize}"
                                        Orientation="{TemplateBinding Orientation}">
                                    <Track.DecreaseButton>
                                        <RepeatButton x:Name="PART_PageUpButton"
                                                        Theme="{StaticResource AeroScrollBarPageButton}"
                                                        Focusable="False"/>
                                    </Track.DecreaseButton>
                                    <Track.IncreaseButton>
                                        <RepeatButton x:Name="PART_PageDownButton"
                                                        Theme="{StaticResource AeroScrollBarPageButton}"
                                                        Focusable="False"/>
                                    </Track.IncreaseButton>
                                    <Thumb Theme="{StaticResource AeroScrollBarThumbHorizontal}"
                                        Classes.scrollBarHideLines="{Binding !IsExpanded, RelativeSource={RelativeSource Mode=TemplatedParent}, Mode=OneWay}" />
                                </Track>
                                
                                <RepeatButton x:Name="PART_LineDownButton"
                                                Theme="{StaticResource AeroScrollBarLineButtonHorizontal}"
                                                Classes="lineButton"
                                                Classes.scrollBarHideIdle="{Binding !IsPointerOver, RelativeSource={RelativeSource Mode=TemplatedParent}, Mode=OneWay}"
                                                Grid.Column="2"
                                                Focusable="False">
                                    <Path Data="M 0 0 L 4 4 L 0 8 Z" />
                                </RepeatButton>
                            </Grid>
                        </Border>
                    </Border>
                </ControlTemplate>
            </Setter>
        </Style>

        <Style Selector="^[AllowAutoHide=False] /template/ Border.repeatBackground">
            <Setter Property="IsVisible" Value="{Binding ViewportSize, RelativeSource={RelativeSource Mode=TemplatedParent}, Mode=OneWay, Converter={x:Static aecnv:DoubleIsNaNConverter.IsNotNaN}}"/>
        </Style>
        <Style Selector="^[AllowAutoHide=False][Maximum=0] /template/ Border.repeatBackground,
                        ^[AllowAutoHide=True] /template/ Border.repeatBackground">
            <Setter Property="IsVisible" Value="False"/>
        </Style>


        <Style Selector="^ /template/ :is(Control).expandedBackground">
            <Setter Property="Opacity" Value="0"/>
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Opacity" Duration="{StaticResource AeroScrollExpandDuration}" />
                </Transitions>
            </Setter>
        </Style>
        <Style Selector="^[IsExpanded=True] /template/ :is(Control).expandedBackground">
            <Setter Property="Opacity" Value="1"/>
            <Setter Property="Margin" Value="0"/>
        </Style>
        

        <Style Selector="^:vertical /template/ :is(Control).root">
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Width" Value="{DynamicResource AeroSmallScrollBarThickness}"/>
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Width" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                    <ThicknessTransition Property="Margin" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                </Transitions>
            </Setter>
        </Style>
        <Style Selector="^[IsExpanded=True]:vertical /template/ :is(Control).root">
            <Setter Property="Width" Value="{DynamicResource AeroScrollBarThickness}"/>
        </Style>

        <Style Selector="^:horizontal /template/ :is(Control).root">
            <Setter Property="VerticalAlignment" Value="Bottom"/>
            <Setter Property="Height" Value="{DynamicResource AeroSmallScrollBarThickness}"/>
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Height" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                    <ThicknessTransition Property="Margin" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                </Transitions>
            </Setter>
        </Style>
        <Style Selector="^[IsExpanded=True]:horizontal /template/ :is(Control).root">
            <Setter Property="Height" Value="{DynamicResource AeroScrollBarThickness}"/>
        </Style>
        
        <Style Selector="^[IsExpanded=False] /template/ :is(Control).root">
            <Setter Property="Margin" Value="1"/>
        </Style>

        <Style Selector="^:horizontal /template/ Border.aeroState.pointerover">
            <Setter Property="Width" Value="{DynamicResource AeroScrollBarThickness}"/>
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Width" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                </Transitions>
            </Setter>
        </Style>
        <Style Selector="^[AllowAutoHide=True][IsExpanded=False]:horizontal /template/ Border.aeroState.pointerover">
            <Setter Property="Width" Value="0"/>
        </Style>

        <Style Selector="^:vertical /template/ Border.aeroState.pointerover">
            <Setter Property="Height" Value="{DynamicResource AeroScrollBarThickness}"/>
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Height" Duration="{StaticResource AeroScrollExpandDuration}" Easing="{StaticResource AeroScrollExpandEase}" />
                </Transitions>
            </Setter>
        </Style>
        <Style Selector="^[AllowAutoHide=True][IsExpanded=False]:vertical /template/ Border.aeroState.pointerover">
            <Setter Property="Height" Value="0"/>
        </Style>
        
        <Style Selector="^[AllowAutoHide=True][IsExpanded=False] /template/ RepeatButton.lineButton">
            <Setter Property="Opacity" Value="0" />
        </Style>
        
        
        
        <Style Selector="^ /template/ RepeatButton.lineButton > Path">
            <Setter Property="Fill" Value="{DynamicResource AeroScrollBarButtonForeground}" />
            <Setter Property="Stroke" Value="{DynamicResource AeroScrollBarButtonIconStroke}"/>
            <Setter Property="StrokeThickness" Value="1"/>

            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
        <Style Selector="^:vertical /template/ RepeatButton.lineButton > Path">
            <Setter Property="Width" Value="8" />
            <Setter Property="Height" Value="4" />
            <Setter Property="Margin" Value="-4,2" />
        </Style>
        <Style Selector="^:horizontal /template/ RepeatButton.lineButton > Path">
            <Setter Property="Width" Value="4" />
            <Setter Property="Height" Value="8" />
            <Setter Property="Margin" Value="-2,-4" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    x:ClassModifier="internal">
    <ControlTheme x:Key="AeroNumericUpDownTextBox"
                TargetType="{x:Type TextBox}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{DynamicResource AeroForeground}" />
        <Setter Property="CaretBrush" Value="{DynamicResource AeroForeground}" />
        <Setter Property="FontSize" Value="{DynamicResource AeroFontSizeNormal}" />
        <Setter Property="SelectionBrush" Value="{DynamicResource HighlightBrush}" />
        <Setter Property="SelectionForegroundBrush" Value="{DynamicResource HighlightForegroundBrush}" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="FocusAdorner" Value="{x:Null}" />
        <Setter Property="ScrollViewer.IsScrollChainingEnabled" Value="True" />
        <Setter Property="ContextFlyout" Value="{StaticResource DefaultTextBoxContextFlyout}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <!--
                    <TextBlock x:Name="PART_FloatingWatermark"
                            Opacity="0.5"
                            Foreground="{DynamicResource AeroForeground}"
                            FontSize="{TemplateBinding FontSize}"
                            IsVisible="False"
                            Text="{TemplateBinding Watermark}"
                            Cursor="{TemplateBinding Cursor}"
                            DockPanel.Dock="Top" />
                    -->
                    <TextBlock x:Name="PART_Watermark"
                            Background="Transparent"
                            Margin="0"
                            Padding="{TemplateBinding Padding}"
                            Opacity="0.5"
                            Foreground="{Binding (TextBlock.Foreground), ElementName=PART_TextPresenter, Mode=OneWay}"
                            Text="{TemplateBinding Watermark}"
                            TextAlignment="{TemplateBinding TextAlignment}"
                            TextWrapping="{TemplateBinding TextWrapping}"
                            IsVisible="{TemplateBinding Text, Converter={x:Static StringConverters.IsNullOrEmpty}}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                    <TextPresenter x:Name="PART_TextPresenter"
                                Background="Transparent"
                                Margin="{TemplateBinding Padding}"
                                TextBlock.Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Text, Mode=TwoWay}"
                                CaretIndex="{TemplateBinding CaretIndex}"
                                SelectionStart="{TemplateBinding SelectionStart}"
                                SelectionEnd="{TemplateBinding SelectionEnd}"
                                TextAlignment="{TemplateBinding TextAlignment}"
                                TextWrapping="{TemplateBinding TextWrapping}"
                                LineHeight="{TemplateBinding LineHeight}"
                                LetterSpacing="{TemplateBinding LetterSpacing}"
                                PasswordChar="{TemplateBinding PasswordChar}"
                                RevealPassword="{TemplateBinding RevealPassword}"
                                SelectionBrush="{TemplateBinding SelectionBrush}"
                                SelectionForegroundBrush="{TemplateBinding SelectionForegroundBrush}"
                                CaretBrush="{TemplateBinding CaretBrush}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                </Panel>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
    
    <ControlTheme x:Key="{x:Type NumericUpDown}" TargetType="NumericUpDown">
        <!--
        <Setter Property="Foreground" Value="{DynamicResource TextControlForeground}" />
        <Setter Property="Background" Value="{DynamicResource TextControlBackground}" />
        <Setter Property="BorderThickness" Value="{DynamicResource TextControlBorderThemeThickness}" />
        <Setter Property="BorderBrush" Value="{DynamicResource TextControlBorderBrush}" />
        <Setter Property="MinHeight" Value="{DynamicResource TextControlThemeMinHeight}" />
        <Setter Property="MinWidth" Value="{DynamicResource TextControlThemeMinWidth}" />
        <Setter Property="FontSize" Value="{DynamicResource ControlContentThemeFontSize}" />
        <Setter Property="Padding" Value="{DynamicResource TextControlThemePadding}" />
        <Setter Property="CornerRadius" Value="{DynamicResource ControlCornerRadius}" />
        -->
        <Setter Property="Background" Value="{DynamicResource AeroTextBoxIdleBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource AeroTextBoxIdleBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Foreground" Value="{DynamicResource AeroForeground}" />
        <Setter Property="Padding" Value="6,3" />
        <Setter Property="FontSize" Value="{DynamicResource AeroFontSizeNormal}" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="CornerRadius" Value="{DynamicResource AeroControlCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate>
                <ButtonSpinner x:Name="PART_Spinner"
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            IsTabStop="False"
                            Padding="0"
                            
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            
                            HorizontalContentAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            
                            AllowSpin="{TemplateBinding AllowSpin}"
                            ShowButtonSpinner="{TemplateBinding ShowButtonSpinner}"
                            DataValidationErrors.Errors="{TemplateBinding (DataValidationErrors.Errors)}"
                            ButtonSpinnerLocation="{TemplateBinding ButtonSpinnerLocation}">
                    <TextBox x:Name="PART_TextBox"
                            Theme="{DynamicResource AeroNumericUpDownTextBox}"
                            Padding="{TemplateBinding Padding}"
                            
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            
                            HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                            
                            Foreground="{TemplateBinding Foreground}"
                            FontSize="{TemplateBinding FontSize}"
                            Watermark="{TemplateBinding Watermark}"
                            IsReadOnly="{TemplateBinding IsReadOnly}"
                            Text="{TemplateBinding Text}"
                            TextAlignment="{TemplateBinding TextAlignment}"
                            AcceptsReturn="False"
                            TextWrapping="NoWrap" />
                </ButtonSpinner>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ae="using:AvaloniaAero"
                    x:ClassModifier="internal">
    <ControlTheme x:Key="GroupBox"
                TargetType="{x:Type HeaderedContentControl}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{DynamicResource AeroGroupBoxBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="5" />
        <Setter Property="Padding" Value="10" />
        <Setter Property="FontSize" Value="{DynamicResource AeroFontSizeNormal}" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid RowDefinitions="Auto,*">
                    <ae:HeaderGapClipper HeaderElement="{Binding #PART_HeaderPresenter, Mode=OneWay}"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                        <Border x:Name="Bd"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}" />
                    </ae:HeaderGapClipper>
                    
                    <Panel Grid.Row="0"
                        Margin="7,0">
                        <Control x:Name="HeaderPresenterSizeHelper"
                                HorizontalAlignment="Stretch"
                                IsHitTestVisible="False"
                                IsTabStop="False"
                                Focusable="False" />
                        <ContentPresenter x:Name="PART_HeaderPresenter"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        BorderThickness="0"
                                        Content="{TemplateBinding Header}"
                                        ContentTemplate="{TemplateBinding HeaderTemplate}"
                                        HorizontalAlignment="Left"
                                        VerticalContentAlignment="Center"
                                        Padding="2,0"
                                        MaxWidth="{Binding #HeaderPresenterSizeHelper.Bounds.Width, Mode=OneWay}" />
                    </Panel>
                            
                    <ContentPresenter x:Name="PART_ContentPresenter"
                                    Background="Transparent"
                                    BorderBrush="Transparent"
                                    BorderThickness="0"
                                    Margin="0,-4,0,0"
                                    Padding="{TemplateBinding Padding}"
                                    Content="{TemplateBinding Content}"
                                    ContentTemplate="{TemplateBinding ContentTemplate}"
                                    Foreground="{TemplateBinding Foreground}"
                                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Grid.Row="1" />
                </Grid>
            </ControlTemplate>
        </Setter>
        <Style Selector="^ /template/ Border#Bd">
            <Setter Property="Margin" Value="0,8,0,0" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    x:ClassModifier="internal">
    <Design.PreviewWith>
        <Border Padding="20">
            <StackPanel Spacing="20">
                <TextBox>Left</TextBox>
                <TextBox TextAlignment="Center">Center</TextBox>
                <TextBox Classes="clearButton">Clear</TextBox>
                <TextBox PasswordChar="*" Classes="revealPasswordButton">Reveal Password</TextBox>
                <TextBox PasswordChar="*" Classes="revealPasswordButton" RevealPassword="True">Password Revealed</TextBox>
                <TextBox Watermark="Watermark"/>
                <TextBox Watermark="Floating Watermark" UseFloatingWatermark="True"/>
                <TextBox Watermark="Floating Watermark" UseFloatingWatermark="True">Content</TextBox>
            </StackPanel>
        </Border>
    </Design.PreviewWith>

    <Thickness x:Key="TextBoxTopHeaderMargin">0,0,0,4</Thickness>
    <StreamGeometry x:Key="TextBoxClearButtonData">M 11.416016,10 20,1.4160156 18.583984,0 10,8.5839846 1.4160156,0 0,1.4160156 8.5839844,10 0,18.583985 1.4160156,20 10,11.416015 18.583984,20 20,18.583985 Z</StreamGeometry>
    <StreamGeometry x:Key="PasswordBoxRevealButtonData">m10.051 7.0032c2.215 0 4.0105 1.7901 4.0105 3.9984s-1.7956 3.9984-4.0105 3.9984c-2.215 0-4.0105-1.7901-4.0105-3.9984s1.7956-3.9984 4.0105-3.9984zm0 1.4994c-1.3844 0-2.5066 1.1188-2.5066 2.499s1.1222 2.499 2.5066 2.499 2.5066-1.1188 2.5066-2.499-1.1222-2.499-2.5066-2.499zm0-5.0026c4.6257 0 8.6188 3.1487 9.7267 7.5613 0.10085 0.40165-0.14399 0.80877-0.54686 0.90931-0.40288 0.10054-0.81122-0.14355-0.91208-0.54521-0.94136-3.7492-4.3361-6.4261-8.2678-6.4261-3.9334 0-7.3292 2.6792-8.2689 6.4306-0.10063 0.40171-0.50884 0.64603-0.91177 0.54571s-0.648-0.5073-0.54737-0.90901c1.106-4.4152 5.1003-7.5667 9.728-7.5667z</StreamGeometry>
    <StreamGeometry x:Key="PasswordBoxHideButtonData">m0.21967 0.21965c-0.26627 0.26627-0.29047 0.68293-0.07262 0.97654l0.07262 0.08412 4.0346 4.0346c-1.922 1.3495-3.3585 3.365-3.9554 5.7495-0.10058 0.4018 0.14362 0.8091 0.54543 0.9097 0.40182 0.1005 0.80909-0.1436 0.90968-0.5455 0.52947-2.1151 1.8371-3.8891 3.5802-5.0341l1.8096 1.8098c-0.70751 0.7215-1.1438 1.71-1.1438 2.8003 0 2.2092 1.7909 4 4 4 1.0904 0 2.0788-0.4363 2.8004-1.1438l5.9193 5.9195c0.2929 0.2929 0.7677 0.2929 1.0606 0 0.2663-0.2662 0.2905-0.6829 0.0726-0.9765l-0.0726-0.0841-6.1135-6.1142 0.0012-0.0015-1.2001-1.1979-2.8699-2.8693 2e-3 -8e-4 -2.8812-2.8782 0.0012-0.0018-1.1333-1.1305-4.3064-4.3058c-0.29289-0.29289-0.76777-0.29289-1.0607 0zm7.9844 9.0458 3.5351 3.5351c-0.45 0.4358-1.0633 0.704-1.7392 0.704-1.3807 0-2.5-1.1193-2.5-2.5 0-0.6759 0.26824-1.2892 0.7041-1.7391zm1.7959-5.7655c-1.0003 0-1.9709 0.14807-2.8889 0.425l1.237 1.2362c0.5358-0.10587 1.0883-0.16119 1.6519-0.16119 3.9231 0 7.3099 2.6803 8.2471 6.4332 0.1004 0.4018 0.5075 0.6462 0.9094 0.5459 0.4019-0.1004 0.6463-0.5075 0.5459-0.9094-1.103-4.417-5.0869-7.5697-9.7024-7.5697zm0.1947 3.5093 3.8013 3.8007c-0.1018-2.0569-1.7488-3.7024-3.8013-3.8007z</StreamGeometry>

    <MenuFlyout x:Key="DefaultTextBoxContextFlyout"
                Placement="Bottom">
        <MenuItem x:Name="TextBoxContextFlyoutCutItem"
                Header="Cut"
                Command="{Binding $parent[TextBox].Cut}"
                IsEnabled="{Binding $parent[TextBox].CanCut}"
                InputGesture="{x:Static TextBox.CutGesture}" />
        <MenuItem x:Name="TextBoxContextFlyoutCopyItem"
                Header="Copy"
                Command="{Binding $parent[TextBox].Copy}"
                IsEnabled="{Binding $parent[TextBox].CanCopy}"
                InputGesture="{x:Static TextBox.CopyGesture}" />
        <MenuItem x:Name="TextBoxContextFlyoutPasteItem"
                Header="Paste"
                Command="{Binding $parent[TextBox].Paste}"
                IsEnabled="{Binding $parent[TextBox].CanPaste}"
                InputGesture="{x:Static TextBox.PasteGesture}" />
    </MenuFlyout>

    <ControlTheme x:Key="AeroTextBoxButton"
                TargetType="{x:Type Button}">
        <Setter Property="Background" Value="{DynamicResource TextControlButtonBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource TextControlButtonBorderBrush}" />
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="MinWidth" Value="34" />
        <Setter Property="Padding" Value="{DynamicResource ButtonPadding}" />
        <Setter Property="Width" Value="{Binding $self.Bounds.Height}"/>
        <Setter Property="TextElement.Foreground" Value="{DynamicResource TextControlButtonForeground}"/>
        <Setter Property="Template">
            <ControlTemplate TargetType="Button">
                <ContentPresenter x:Name="PART_ContentPresenter"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Content="{TemplateBinding Content}"
                                Padding="{TemplateBinding Padding}" />
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="{DynamicResource TextControlButtonBackgroundPointerOver}" />
            <Setter Property="BorderBrush" Value="{DynamicResource TextControlButtonBorderBrushPointerOver}" />
            <Setter Property="Foreground" Value="{DynamicResource TextControlButtonForegroundPointerOver}" />
        </Style>

        <Style Selector="^:pressed /template/ ContentPresenter">
            <Setter Property="Background" Value="{DynamicResource TextControlButtonBackgroundPressed}" />
            <Setter Property="BorderBrush" Value="{DynamicResource TextControlButtonBorderBrushPressed}" />
            <Setter Property="Foreground" Value="{DynamicResource TextControlButtonForegroundPressed}" />
        </Style>

        <Style Selector="^:disabled /template/ ContentPresenter">
            <Setter Property="Opacity" Value="0" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="AeroTextBoxToggleButton"
                TargetType="{x:Type ToggleButton}"
                BasedOn="{StaticResource AeroTextBoxButton}">
        <Setter Property="Template">
            <ControlTemplate TargetType="ToggleButton">
                <ContentPresenter x:Name="PART_ContentPresenter"
                                Background="{TemplateBinding Background}"
                                Content="{TemplateBinding Content}"
                                Padding="{TemplateBinding Padding}" />
            </ControlTemplate>
        </Setter>

        <Style Selector="^:checked /template/ ContentPresenter, ^:indeterminate /template/ ContentPresenter">
            <Setter Property="Background" Value="{DynamicResource TextControlButtonBackgroundPressed}" />
            <Setter Property="BorderBrush" Value="{DynamicResource TextControlButtonBorderBrushPressed}" />
            <Setter Property="Foreground" Value="{DynamicResource TextControlButtonForegroundPressed}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type TextBox}" TargetType="TextBox">
        <Setter Property="Background" Value="{DynamicResource AeroTextBoxIdleBackground}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AeroTextBoxIdleBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource AeroForeground}"/>
        <Setter Property="CaretBrush" Value="{DynamicResource AeroForeground}" />
        <Setter Property="Padding" Value="3"/>
        <Setter Property="FontSize" Value="{DynamicResource AeroFontSizeNormal}"/>
        <Setter Property="SelectionBrush" Value="{DynamicResource HighlightBrush}"/>
        <Setter Property="SelectionForegroundBrush" Value="{DynamicResource HighlightForegroundBrush}"/>
        <Setter Property="CornerRadius" Value="{DynamicResource AeroControlCornerRadius}" />
        <!--
        <Setter Property="MinHeight" Value="{DynamicResource TextControlThemeMinHeight}" />
        <Setter Property="MinWidth" Value="{DynamicResource TextControlThemeMinWidth}" />
        -->
        <Setter Property="FocusAdorner" Value="{x:Null}" />
        <Setter Property="ScrollViewer.IsScrollChainingEnabled" Value="True" />
        <Setter Property="ContextFlyout" Value="{StaticResource DefaultTextBoxContextFlyout}" />
        <Setter Property="Template">
            <ControlTemplate>
                <DataValidationErrors>
                    <Panel>
                        <Border x:Name="BgElement"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}" />
                        <!--
                                MinWidth="{TemplateBinding MinWidth}"
                                MinHeight="{TemplateBinding MinHeight}"
                        -->
                        <Grid ColumnDefinitions="Auto,*,Auto"
                            Margin="{TemplateBinding BorderThickness}">
                            <ContentPresenter Grid.Column="0"
                                            Grid.ColumnSpan="1"
                                            Content="{TemplateBinding InnerLeftContent}" />
                            <DockPanel x:Name="PART_InnerDockPanel"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="1"
                                    Cursor="IBeam"
                                    Margin="{TemplateBinding Padding}">
                                <TextBlock x:Name="PART_FloatingWatermark"
                                        Opacity="0.5"
                                        Foreground="{DynamicResource AeroForeground}"
                                        FontSize="{TemplateBinding FontSize}"
                                        IsVisible="False"
                                        Text="{TemplateBinding Watermark}"
                                        Cursor="{TemplateBinding Cursor}"
                                        DockPanel.Dock="Top" />
                                <ScrollViewer x:Name="PART_ScrollViewer"
                                            HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                            VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}"
                                            IsScrollChainingEnabled="{TemplateBinding (ScrollViewer.IsScrollChainingEnabled)}"
                                            AllowAutoHide="{TemplateBinding (ScrollViewer.AllowAutoHide)}"
                                            BringIntoViewOnFocusChange="{TemplateBinding (ScrollViewer.BringIntoViewOnFocusChange)}">
                                    <Panel>
                                        <TextBlock x:Name="PART_Watermark"
                                                Opacity="0.5"
                                                Foreground="{Binding (TextBlock.Foreground), ElementName=PART_TextPresenter, Mode=OneWay}"
                                                Text="{TemplateBinding Watermark}"
                                                TextAlignment="{TemplateBinding TextAlignment}"
                                                TextWrapping="{TemplateBinding TextWrapping}"
                                                IsVisible="{TemplateBinding Text, Converter={x:Static StringConverters.IsNullOrEmpty}}"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                        <TextPresenter x:Name="PART_TextPresenter"
                                                    TextBlock.Foreground="{TemplateBinding Foreground}"
                                                    Text="{TemplateBinding Text, Mode=TwoWay}"
                                                    CaretIndex="{TemplateBinding CaretIndex}"
                                                    SelectionStart="{TemplateBinding SelectionStart}"
                                                    SelectionEnd="{TemplateBinding SelectionEnd}"
                                                    TextAlignment="{TemplateBinding TextAlignment}"
                                                    TextWrapping="{TemplateBinding TextWrapping}"
                                                    LineHeight="{TemplateBinding LineHeight}"
                                                    LetterSpacing="{TemplateBinding LetterSpacing}"
                                                    PasswordChar="{TemplateBinding PasswordChar}"
                                                    RevealPassword="{TemplateBinding RevealPassword}"
                                                    SelectionBrush="{TemplateBinding SelectionBrush}"
                                                    SelectionForegroundBrush="{TemplateBinding SelectionForegroundBrush}"
                                                    CaretBrush="{TemplateBinding CaretBrush}"
                                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                    </Panel>
                                </ScrollViewer>
                            </DockPanel>
                            <ContentPresenter Content="{TemplateBinding InnerRightContent}"
                                            Grid.Column="2"
                                            Grid.ColumnSpan="1" />
                        </Grid>
                    </Panel>
                </DataValidationErrors>
            </ControlTemplate>
        </Setter>

        <!-- Disabled State -->
        <Style Selector="^:disabled">
            <Style Selector="^ /template/ Border#BgElement">
                <Setter Property="Background" Value="{DynamicResource AeroTextBoxDisabledBackground}" />
                <Setter Property="BorderBrush" Value="{DynamicResource AeroTextBoxDisabledBorderBrush}" />
            </Style>

            <Style Selector="^ /template/ TextPresenter#PART_TextPresenter">
                <Setter Property="TextBlock.Foreground" Value="{DynamicResource AeroDisabledForeground}" />
            </Style>
        </Style>

        <!-- PointerOver State -->
        <Style Selector="^:pointerover">
            <Style Selector="^ /template/ Border#BgElement">
                <Setter Property="BorderBrush" Value="{DynamicResource AeroTextBoxHoverBorderBrush}"/>
                <Setter Property="Background" Value="{DynamicResource AeroTextBoxHoverBackground}" />
            </Style>
        </Style>

        <!-- Focused State -->
        <Style Selector="^:focus">
            <Style Selector="^ /template/ Border#BgElement">
                <Setter Property="Background" Value="{DynamicResource AeroTextBoxFocusBackground}" />
                <Setter Property="BorderBrush" Value="{DynamicResource AeroTextBoxFocusBorderBrush}" />
            </Style>
        </Style>

        <!-- Error State -->
        <Style Selector="^:error /template/ Border#BgElement">
            <Setter Property="BorderBrush" Value="{DynamicResource AeroTextBoxErrorBorderBrush}" />
        </Style>

        <Style Selector="^[UseFloatingWatermark=true]:not(:empty) /template/ TextBlock#PART_FloatingWatermark">
            <Setter Property="IsVisible" Value="True" />
        </Style>

        <Style Selector="^.revealPasswordButton[AcceptsReturn=False][IsReadOnly=False]:not(TextBox:empty)">
            <Setter Property="InnerRightContent">
                <Template>
                    <ToggleButton Theme="{StaticResource AeroTextBoxToggleButton}"
                                    IsChecked="{Binding $parent[TextBox].RevealPassword, Mode=TwoWay}"
                                    ClipToBounds="True">
                        <Panel>
                            <PathIcon Data="{StaticResource PasswordBoxRevealButtonData}"
                                    Height="8" Width="12"
                                    IsVisible="{Binding !$parent[ToggleButton].IsChecked}"/>
                            <PathIcon Data="{StaticResource PasswordBoxHideButtonData}"
                                    Height="12" Width="12"
                                    IsVisible="{Binding $parent[ToggleButton].IsChecked}"/>
                        </Panel>
                    </ToggleButton>
                </Template>
            </Setter>
        </Style>

        <Style Selector="^.clearButton[AcceptsReturn=False][IsReadOnly=False]:focus:not(TextBox:empty)">
            <Setter Property="InnerRightContent">
                <Template>
                    <Button Theme="{StaticResource AeroTextBoxButton}"
                            Command="{Binding $parent[TextBox].Clear}"
                            ClipToBounds="True">
                        <PathIcon Data="{StaticResource TextBoxClearButtonData}"
                                Width="10"
                                Height="10" />
                    </Button>
                </Template>
            </Setter>
        </Style>
    </ControlTheme>
</ResourceDictionary>

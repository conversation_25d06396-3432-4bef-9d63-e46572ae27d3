<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    x:ClassModifier="internal">
    <ControlTheme x:Key="{x:Type Window}" TargetType="Window">
        <Setter Property="Background" Value="{DynamicResource AeroWindowBackground}"/>
        <Setter Property="TransparencyBackgroundFallback" Value="{DynamicResource AeroWindowBackground}" />
        <Setter Property="Foreground" Value="{DynamicResource AeroForeground}"/>
        <Setter Property="FontSize" Value="{DynamicResource AeroFontSizeNormal}"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border x:Name="PART_TransparencyFallback" IsHitTestVisible="False" />
                    <Border Background="{TemplateBinding Background}" IsHitTestVisible="False" />
                    <Panel Background="Transparent" Margin="{TemplateBinding WindowDecorationMargin}" />
                    <VisualLayerManager>
                        <VisualLayerManager.ChromeOverlayLayer>
                            <TitleBar />
                        </VisualLayerManager.ChromeOverlayLayer>
                        <ContentPresenter x:Name="PART_ContentPresenter"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        Content="{TemplateBinding Content}"
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </VisualLayerManager>
                </Panel>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    x:ClassModifier="internal">
    <ControlTheme x:Key="{x:Type RadioButton}"
                TargetType="{x:Type RadioButton}"
                BasedOn="{StaticResource AeroCheckBoxBaseStyle}">
        
        <Style Selector="^ /template/ Border#Outer">
            <Setter Property="Width" Value="12" />
            <Setter Property="Height" Value="12" />
        </Style>

        <Style Selector="^ /template/ Border#Outer,
                        ^ /template/ Border#Mid,
                        ^ /template/ Border#Inner">
            <Setter Property="CornerRadius" Value="24" />
        </Style>


        <Style Selector="^ /template/ Path#CheckMark">
            <Setter Property="Data">
                <EllipseGeometry Rect="0,0,6,6" />
            </Setter>
            <Setter Property="StrokeThickness" Value="1" />
            <Setter Property="Stretch" Value="None" />
            <Setter Property="UseLayoutRounding" Value="False" />
        </Style>


        <Style Selector="^ /template/ Path#CheckMark">
            <Setter Property="Opacity" Value="0"/>
            <Setter Property="Fill" Value="{DynamicResource AeroRadioButtonIdleCircleFill}"/>
            <Setter Property="Stroke" Value="{DynamicResource AeroCheckBoxIdleCheckMarkBrush}"/>
        </Style>
        <Style Selector="^:pointerover /template/ Path#CheckMark">
            <Setter Property="Fill" Value="{DynamicResource AeroRadioButtonHoverCircleFill}"/>
            <Setter Property="Stroke" Value="{DynamicResource AeroCheckBoxHoverCheckMarkBrush}"/>
        </Style>
        <Style Selector="^:pressed /template/ Path#CheckMark">
            <Setter Property="Fill" Value="{DynamicResource AeroRadioButtonPressedCircleFill}"/>
            <Setter Property="Stroke" Value="{DynamicResource AeroCheckBoxPressedCheckMarkBrush}"/>
        </Style>
        <Style Selector="^:disabled /template/ Path#CheckMark">
            <Setter Property="Fill" Value="{DynamicResource AeroRadioButtonDisabledCircleFill}"/>
            <Setter Property="Stroke" Value="{DynamicResource AeroCheckBoxDisabledCheckMarkBrush}"/>
        </Style>
    </ControlTheme>
</ResourceDictionary>
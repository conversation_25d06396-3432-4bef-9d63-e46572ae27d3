<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    x:ClassModifier="internal">
    <ControlTheme x:Key="AeroCheckBoxBaseStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Foreground" Value="{DynamicResource AeroForeground}"/>
        <Setter Property="Background" Value="{DynamicResource AeroCheckBoxIdleBackground}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxIdleOuterBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="4,0,0,0"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="Auto,*">
                    <Border x:Name="Outer"
                            Classes="aeroState2"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Width="13"
                            Height="13">
                        <Border x:Name="Mid"
                                Classes="aeroState2"
                                BorderBrush="{DynamicResource AeroCheckBoxIdleMiddleBorderBrush}"
                                BorderThickness="1">
                            <Border x:Name="Inner"
                                    Classes="aeroState2"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{DynamicResource AeroCheckBoxIdleInnerBorderBrush}"
                                    BorderThickness="1">
                                <Panel>
                                    <Path x:Name="CheckMark"
                                        Stroke="{DynamicResource AeroCheckBoxIdleCheckMarkBrush}"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Opacity="0" />
                                    <Rectangle x:Name="IndeterminateMark"
                                            Width="5" Height="5"
                                            Fill="{DynamicResource AeroCheckBoxIdleCheckMarkBrush}"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Opacity="0" />
                                </Panel>
                            </Border>
                        </Border>
                    </Border>
                    <ContentPresenter Name="PART_ContentPresenter" TextBlock.Foreground="{TemplateBinding Foreground}" ContentTemplate="{TemplateBinding ContentTemplate}" Content="{TemplateBinding Content}" Margin="{TemplateBinding Padding}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" IsVisible="{TemplateBinding Content, Converter={x:Static ObjectConverters.IsNotNull}}" Grid.Column="1"/>
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^ /template/ Path#CheckMark,
                        ^ /template/ Rectangle#IndeterminateMark">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Opacity"
                                    Duration="{StaticResource AeroFadeDuration}" />
                </Transitions>
            </Setter>
        </Style>


        <!-- pointerover (hover) state -->
        <Style Selector="^:pointerover">
            <Style Selector="^ /template/ Border#Outer">
                <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxHoverOuterBorderBrush}" />
            </Style>
            <Style Selector="^ /template/ Border#Mid">
                <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxHoverMiddleBorderBrush}" />
            </Style>
            <Style Selector="^ /template/ Border#Inner">
                <Setter Property="Background" Value="{DynamicResource AeroCheckBoxHoverBackground}" />
                <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxHoverInnerBorderBrush}" />
            </Style>

            <Style Selector="^ /template/ Path#CheckMark">
                <Setter Property="Stroke" Value="{DynamicResource AeroCheckBoxHoverCheckMarkBrush}"/>
            </Style>
            <Style Selector="^ /template/ Rectangle#IndeterminateMark">
                <Setter Property="Fill" Value="{DynamicResource AeroCheckBoxHoverCheckMarkBrush}"/>
            </Style>
        </Style>


        <!-- pressed state -->
        <Style Selector="^:pressed">
            <Style Selector="^ /template/ Border#Outer">
                <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxPressedOuterBorderBrush}" />
            </Style>
            <Style Selector="^ /template/ Border#Mid">
                <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxPressedMiddleBorderBrush}" />
            </Style>
            <Style Selector="^ /template/ Border#Inner">
                <Setter Property="Background" Value="{DynamicResource AeroCheckBoxPressedBackground}" />
                <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxPressedInnerBorderBrush}" />
            </Style>
            
            <Style Selector="^ /template/ Path#CheckMark">
                <Setter Property="Stroke" Value="{DynamicResource AeroCheckBoxPressedCheckMarkBrush}"/>
            </Style>
            <Style Selector="^ /template/ Rectangle#IndeterminateMark">
                <Setter Property="Fill" Value="{DynamicResource AeroCheckBoxPressedCheckMarkBrush}"/>
            </Style>
        </Style>


        <Style Selector="^:checked /template/ Path#CheckMark">
            <Setter Property="Opacity" Value="1" />
        </Style>
        <Style Selector="^:indeterminate /template/ Rectangle#IndeterminateMark">
            <Setter Property="Opacity" Value="1" />
        </Style>



        <!-- disabled state -->
        <Style Selector="^:disabled">
            <Style Selector="^ /template/ Border#Outer">
                <Setter Property="Background" Value="{DynamicResource AeroCheckBoxDisabledBackground}" />
                <Setter Property="BorderBrush" Value="{DynamicResource AeroCheckBoxDisabledBorderBrush}" />
            </Style>
            <Style Selector="^ /template/ Border#Mid,
                            ^ /template/ Border#Inner">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="Transparent" />
            </Style>

            <Style Selector="^ /template/ Path#CheckMark">
                <Setter Property="Stroke" Value="{DynamicResource AeroCheckBoxDisabledCheckMarkBrush}"/>
            </Style>
            <Style Selector="^ /template/ Rectangle#IndeterminateMark">
                <Setter Property="Fill" Value="{DynamicResource AeroCheckBoxDisabledCheckMarkBrush}"/>
            </Style>
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type CheckBox}"
                TargetType="{x:Type CheckBox}"
                BasedOn="{StaticResource AeroCheckBoxBaseStyle}">
        <Style Selector="^ /template/ Path#CheckMark">
            <Setter Property="Data" Value="M 0 4 L 3 7 L 6 0" />
            <Setter Property="StrokeThickness" Value="2" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    x:ClassModifier="internal">
    <ControlTheme x:Key="{x:Type ScrollViewer}" TargetType="ScrollViewer">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="*,Auto" RowDefinitions="*,Auto">
                    <ScrollContentPresenter x:Name="PART_ContentPresenter"
                                            Grid.Row="0"
                                            Grid.Column="0"
                                            Background="{TemplateBinding Background}"
                                            HorizontalSnapPointsType="{TemplateBinding HorizontalSnapPointsType}"
                                            VerticalSnapPointsType="{TemplateBinding VerticalSnapPointsType}"
                                            HorizontalSnapPointsAlignment="{TemplateBinding HorizontalSnapPointsAlignment}"
                                            VerticalSnapPointsAlignment="{TemplateBinding VerticalSnapPointsAlignment}"
                                            Padding="{TemplateBinding Padding}">
                        <ScrollContentPresenter.GestureRecognizers>
                            <ScrollGestureRecognizer CanHorizontallyScroll="{Binding CanHorizontallyScroll, ElementName=PART_ContentPresenter}"
                                                    CanVerticallyScroll="{Binding CanVerticallyScroll, ElementName=PART_ContentPresenter}"
                                                    IsScrollInertiaEnabled="{Binding (ScrollViewer.IsScrollInertiaEnabled), ElementName=PART_ContentPresenter}" />
                        </ScrollContentPresenter.GestureRecognizers>
                    </ScrollContentPresenter>
                    <ScrollBar Name="PART_HorizontalScrollBar"
                                        Orientation="Horizontal"
                                        Grid.Row="1" />
                    <ScrollBar Name="PART_VerticalScrollBar"
                                        Orientation="Vertical"
                                        Grid.Column="1" />
                    
                    <Rectangle x:Name="ScrollBarsSeparator"
                        Fill="{DynamicResource AeroScrollViewerCornerBackground}"
                        Grid.Row="1"
                        Grid.Column="1"
                        IsHitTestVisible="False" />
                </Grid>
            </ControlTemplate>
        </Setter>
        
        <Style Selector="^ /template/ Rectangle#ScrollBarsSeparator">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Opacity" Duration="{StaticResource AeroScrollExpandDuration}" />
                </Transitions>
            </Setter>
        </Style>
        <Style Selector="^[AllowAutoHide=True][IsExpanded=False] /template/ Rectangle#ScrollBarsSeparator">
            <Setter Property="Opacity" Value="0" />
        </Style>


        <Style Selector="^[AllowAutoHide=True] /template/ ScrollContentPresenter#PART_ContentPresenter">
            <Setter Property="Grid.RowSpan" Value="2" />
            <Setter Property="Grid.ColumnSpan" Value="2" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    x:ClassModifier="internal">
    <x:Double x:Key="MenuBarHeight">20</x:Double>

    <ControlTheme x:Key="AeroTopLevelMenuItem" TargetType="MenuItem">
        <Setter Property="Foreground" Value="{DynamicResource MenuFlyoutItemForeground}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="3" />
        <Setter Property="FontSize" Value="{DynamicResource AeroFontSizeNormal}" />
        <Setter Property="Padding" Value="8,0" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel x:Name="PART_LayoutRoot">
                    <Border x:Name="BackgroundBorder"
                            Margin="1"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}" />
                    <ContentPresenter x:Name="PART_HeaderPresenter"
                                    Content="{TemplateBinding Header}"
                                    VerticalAlignment="Center"
                                    HorizontalAlignment="Stretch"
                                    RecognizesAccessKey="True"
                                    Margin="{TemplateBinding Padding}"/>
                    <Popup x:Name="PART_Popup"
                        WindowManagerAddShadowHint="True"
                        MinWidth="{Binding Bounds.Width, RelativeSource={RelativeSource TemplatedParent}}"
                        IsLightDismissEnabled="True"
                        IsOpen="{TemplateBinding IsSubMenuOpen, Mode=TwoWay}"
                        OverlayInputPassThroughElement="{Binding $parent[Menu]}">
                        <Border Background="{DynamicResource MenuFlyoutPresenterBackground}"
                                BorderBrush="{DynamicResource MenuFlyoutPresenterBorderBrush}"
                                BorderThickness="{DynamicResource MenuFlyoutPresenterBorderThemeThickness}"
                                Padding="{DynamicResource MenuFlyoutPresenterThemePadding}"
                                MaxWidth="{DynamicResource FlyoutThemeMaxWidth}"
                                MinHeight="{DynamicResource MenuFlyoutThemeMinHeight}"
                                HorizontalAlignment="Stretch"
                                CornerRadius="{DynamicResource OverlayCornerRadius}">
                            <ScrollViewer Theme="{DynamicResource FluentMenuScrollViewer}">
                                <ItemsPresenter x:Name="PART_ItemsPresenter"
                                                ItemsPanel="{TemplateBinding ItemsPanel}"
                                                Margin="{DynamicResource MenuFlyoutScrollerMargin}"
                                                Grid.IsSharedSizeScope="True" />
                            </ScrollViewer>
                        </Border>
                    </Popup>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^[IsSubMenuOpen=False]:pointerover,
                        ^[IsSubMenuOpen=False]:focus">
            <Style Selector="^ /template/ Border#BackgroundBorder">
                <Setter Property="Background" Value="{DynamicResource AeroMenuBarItemHoverBackground}" />
                <Setter Property="BorderBrush" Value="{DynamicResource AeroMenuBarItemHoverBorderBrush}" />
                <Setter Property="BoxShadow" Value="inset 0 0 0 1 #40FFFFFF" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter#PART_HeaderPresenter">
                <Setter Property="Foreground" Value="{DynamicResource MenuFlyoutItemForegroundPointerOver}" />
            </Style>
        </Style>

        <Style Selector="^[IsSubMenuOpen=True]">
            <Style Selector="^ /template/ Border#BackgroundBorder">
                <Setter Property="Background" Value="{DynamicResource AeroMenuBarItemSelectedBackground}" />
                <Setter Property="BorderBrush" Value="{DynamicResource AeroMenuBarItemSelectedBorderBrush}" />
                <Setter Property="BoxShadow" Value="inset 2 2 2 0 #40000000" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter#PART_HeaderPresenter">
                <Setter Property="Foreground" Value="{DynamicResource MenuFlyoutItemForegroundPressed}" />
            </Style>
        </Style>

        <Style Selector="^:disabled">
            <Style Selector="^ /template/ Border#PART_LayoutRoot">
                <Setter Property="Background" Value="{DynamicResource MenuFlyoutItemBackgroundDisabled}" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter#PART_HeaderPresenter">
                <Setter Property="Foreground" Value="{DynamicResource MenuFlyoutItemForegroundDisabled}" />
            </Style>
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type Menu}" TargetType="Menu">
        <Setter Property="Background" Value="{DynamicResource AeroMenuBarBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource AeroMenuBarSideBorderBrush}" />
        <Setter Property="BorderThickness" Value="1,0" />
        <Setter Property="Height" Value="{DynamicResource MenuBarHeight}" />
        <Setter Property="ItemContainerTheme" Value="{DynamicResource AeroTopLevelMenuItem}" />
        <Setter Property="Template">
            <ControlTemplate>
                <DockPanel>
                    <Rectangle Classes="bottomBorder outer"
                            DockPanel.Dock="Bottom" />
                    <Rectangle Classes="bottomBorder"
                            DockPanel.Dock="Bottom" />
                        
                    <Panel>
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            HorizontalAlignment="Stretch"
                            Padding="{TemplateBinding Padding}" />
                    <Rectangle Classes="shine"
                                Height="7"
                                VerticalAlignment="Top"
                                Fill="{DynamicResource AeroMenuBarShine}" />
                    <ItemsPresenter x:Name="PART_ItemsPresenter"
                                    ItemsPanel="{TemplateBinding ItemsPanel}"
                                    VerticalAlignment="Stretch"
                                    KeyboardNavigation.TabNavigation="Continue" />
                    </Panel>
                </DockPanel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^ /template/ Rectangle.bottomBorder">
            <Setter Property="Height" Value="1" />
        </Style>
        <Style Selector="^ /template/ Rectangle.bottomBorder.outer">
            <Setter Property="Fill" Value="{DynamicResource AeroMenuBarBottomOuterBorderBrush}" />
        </Style>
        <Style Selector="^ /template/ Rectangle.bottomBorder:not(.outer)">
            <Setter Property="Fill" Value="{DynamicResource AeroMenuBarBottomInnerBorderBrush}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:ClassModifier="internal">
    <!--
    <StyleInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/FluentControls.xaml" />
    -->
    <Styles.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/Button.axaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/Button_RepeatButton_ToggleButton.axaml" />
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/RadioButton.axaml" />
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/ButtonSpinner.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/Expander.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/ProgressBar.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/AutoCompleteBox.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ButtonSpinner.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/Calendar.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/CalendarButton.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/CalendarDatePicker.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/CalendarDayButton.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/CalendarItem.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/CaptionButtons.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/Carousel.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/CheckBox.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/FlyoutPresenter.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/GridSplitter.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/HeaderedContentControl.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ItemsControl.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/Label.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ListBox.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ListBoxItem.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/MenuScrollViewer.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/Menu.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/MenuItem.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/MenuFlyoutPresenter.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/NativeMenuBar.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/NotificationCard.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/NumericUpDown.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/OverlayPopupHost.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/PopupRoot.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/PathIcon.xaml" />
                -->
                <!-- RepeatButton -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/ScrollBar.axaml" />
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/ScrollViewer.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/Separator.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/SplitButton.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/SplitView.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/TabControl.axaml" />
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/TabItem.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/TabStrip.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/TabStripItem.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/TextBox.axaml" />
                <!-- ToggleButton -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/ToggleSwitch.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ToolTip.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/TitleBar.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/TransitioningContentControl.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/TreeView.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/TreeViewItem.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/WindowNotificationManager.xaml" />
                -->
                <ResourceInclude Source="avares://AvaloniaAero/ControlStyles/Window.axaml" />
                <!--
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ComboBox.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ComboBoxItem.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ContentControl.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ContextMenu.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/DataValidationErrors.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/DateTimePickerShared.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/DatePicker.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/TimePicker.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/DropDownButton.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/EmbeddableControlRoot.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/Slider.xaml" />
                <!- -  ManagedFileChooser comes last because it uses (and overrides) styles for a multitude of other controls...the dialogs were originally UserControls, after all  - ->
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/ManagedFileChooser.xaml" />
                <ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Controls/SelectableTextBlock.xaml"/>
                -->
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Styles.Resources>
</Styles>

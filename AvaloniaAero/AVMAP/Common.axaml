<Styles xmlns="https://github.com/avaloniaui"
	xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	x:ClassModifier="internal">
	<Styles.Resources>
		<ResourceDictionary>
			<ResourceDictionary.MergedDictionaries>
				<ResourceInclude Source="avares://AvaloniaAero/AVMAP/Metrics.axaml"/>

				<!--TEMP Fluent placeholders-->
				<!--
				<ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Accents/BaseColorsPalette.xaml" />
				<accents:SystemAccentColors />
				<fluent:ColorPaletteResourcesCollection />

				<ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Accents/BaseResources.xaml" />
				<ResourceInclude Source="avares://AvaloniaAero.Placeholders.Themes.Fluent/Accents/FluentControlResources.xaml" />
				-->
			</ResourceDictionary.MergedDictionaries>
		</ResourceDictionary>
	</Styles.Resources>
</Styles>
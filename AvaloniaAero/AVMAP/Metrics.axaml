<ResourceDictionary xmlns="https://github.com/avaloniaui"
                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                xmlns:sys="using:System"
                x:ClassModifier="internal">
    <sys:Double x:Key="AeroFontSizeSmall">10</sys:Double>
    <sys:Double x:Key="AeroFontSizeNormal">12</sys:Double>
    <sys:Double x:Key="AeroFontSizeLarge">16</sys:Double>

    <CornerRadius x:Key="AeroControlCornerRadius">3</CornerRadius>

    <StaticResource x:Key="ControlContentThemeFontSize" ResourceKey="AeroFontSizeNormal" />
    
    <FontFamily x:Key="ContentControlThemeFontFamily">avares://AvaloniaAero/Fonts#Inter</FontFamily>




    <sys:Double x:Key="AeroScrollBarThickness">18</sys:Double>
    <sys:Double x:Key="AeroSmallScrollBarThickness">9</sys:Double>
    <sys:Double x:Key="AeroScrollBarLineButtonExtent">16</sys:Double>
    <StaticResource x:Key="AeroScrollBarThumbMinimumExtent" ResourceKey="AeroScrollBarLineButtonExtent" />
    
    <QuinticEaseInOut x:Key="AeroScrollExpandEase"/>
    <sys:TimeSpan x:Key="AeroScrollExpandDuration">0:0:0.25</sys:TimeSpan>
</ResourceDictionary>
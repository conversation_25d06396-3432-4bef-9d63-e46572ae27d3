using System;
using Eto.Forms;
using Launcher.Theming;
using Microsoft.Extensions.Logging;

namespace Launcher.Wpf
{
	class Program
	{
		[STAThread]
		public static void Main(string[] args)
		{
			var app = new Application(Eto.Platforms.Wpf);

			// Initialize theme manager
			try
			{
				var themeManager = ThemeManager.Instance;
				themeManager.Initialize();

				// Subscribe to theme changes to apply WPF-specific styling
				themeManager.ThemeChanged += (sender, theme) =>
				{
					WpfThemeHandler.ApplyWpfTheme(theme);
				};

				Launcher.Logger.LoggerInstance.LogInformation("Theme system initialized successfully");
			}
			catch (Exception ex)
			{
				Launcher.Logger.LoggerInstance.LogError($"Failed to initialize theme system: {ex.Message}");
			}

			app.Run(new MainWindow());
		}
	}
}
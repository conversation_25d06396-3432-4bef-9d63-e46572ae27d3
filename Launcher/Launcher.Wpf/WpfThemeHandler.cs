using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using Microsoft.Extensions.Logging;
using Launcher.Theming;

namespace Launcher.Wpf;

/// <summary>
/// Handles WPF-specific theme operations, including dark title bar support
/// </summary>
public static class WpfThemeHandler
{
    // DWM API constants for dark mode title bar
    private const int DWMWA_USE_IMMERSIVE_DARK_MODE_BEFORE_20H1 = 19;
    private const int DWMWA_USE_IMMERSIVE_DARK_MODE = 20;

    [DllImport("dwmapi.dll")]
    private static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);

    [DllImport("kernel32.dll")]
    private static extern uint GetVersion();

    /// <summary>
    /// Applies dark mode title bar to a WPF window if supported
    /// </summary>
    /// <param name="window">The WPF window to apply dark mode to</param>
    /// <param name="isDarkMode">Whether to enable dark mode</param>
    /// <returns>True if dark mode was successfully applied</returns>
    public static bool ApplyDarkTitleBar(Window window, bool isDarkMode)
    {
        if (window == null)
            return false;

        try
        {
            var windowHelper = new WindowInteropHelper(window);
            var hwnd = windowHelper.Handle;

            if (hwnd == IntPtr.Zero)
            {
                // Window not yet created, hook into SourceInitialized event
                window.SourceInitialized += (sender, e) =>
                {
                    var helper = new WindowInteropHelper((Window)sender!);
                    SetDarkMode(helper.Handle, isDarkMode);
                };
                return true;
            }
            else
            {
                return SetDarkMode(hwnd, isDarkMode);
            }
        }
        catch (Exception ex)
        {
            Launcher.Logger.LoggerInstance.LogWarning($"Failed to apply dark title bar: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Sets dark mode for a window handle
    /// </summary>
    /// <param name="hwnd">Window handle</param>
    /// <param name="enabled">Whether to enable dark mode</param>
    /// <returns>True if successful</returns>
    private static bool SetDarkMode(IntPtr hwnd, bool enabled)
    {
        if (!IsWindows10OrGreater())
        {
            Launcher.Logger.LoggerInstance.LogInformation("Dark mode title bar requires Windows 10 or later");
            return false;
        }

        try
        {
            var attribute = DWMWA_USE_IMMERSIVE_DARK_MODE_BEFORE_20H1;
            
            // Use the newer attribute for Windows 10 version 2004 and later
            if (IsWindows10Version2004OrGreater())
            {
                attribute = DWMWA_USE_IMMERSIVE_DARK_MODE;
            }

            int useImmersiveDarkMode = enabled ? 1 : 0;
            int result = DwmSetWindowAttribute(hwnd, attribute, ref useImmersiveDarkMode, sizeof(int));
            
            if (result == 0)
            {
                Launcher.Logger.LoggerInstance.LogInformation($"Successfully applied {(enabled ? "dark" : "light")} title bar");
                return true;
            }
            else
            {
                Launcher.Logger.LoggerInstance.LogWarning($"DwmSetWindowAttribute failed with result: {result}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Launcher.Logger.LoggerInstance.LogError($"Error setting dark mode: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Checks if the current Windows version is 10 or greater
    /// </summary>
    private static bool IsWindows10OrGreater()
    {
        var version = Environment.OSVersion.Version;
        return version.Major >= 10;
    }

    /// <summary>
    /// Checks if the current Windows version is 10 version 2004 (build 19041) or greater
    /// </summary>
    private static bool IsWindows10Version2004OrGreater()
    {
        var version = Environment.OSVersion.Version;
        return version.Major > 10 || (version.Major == 10 && version.Build >= 19041);
    }

    /// <summary>
    /// Applies WPF-specific theme styling
    /// </summary>
    /// <param name="theme">The theme configuration to apply</param>
    public static void ApplyWpfTheme(ThemeConfiguration theme)
    {
        try
        {
            // Apply theme to all current WPF windows
            foreach (Window window in Application.Current.Windows)
            {
                ApplyDarkTitleBar(window, theme.Theme == AppTheme.Dark);
                
                // Apply background color to window
                if (theme.Theme == AppTheme.Dark)
                {
                    window.Background = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(
                            theme.Background.Rb, 
                            theme.Background.Gb, 
                            theme.Background.Bb));
                }
                else
                {
                    window.Background = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(
                            theme.Background.Rb, 
                            theme.Background.Gb, 
                            theme.Background.Bb));
                }
            }

            Launcher.Logger.LoggerInstance.LogInformation($"Applied WPF-specific {theme.Theme} theme styling");
        }
        catch (Exception ex)
        {
            Launcher.Logger.LoggerInstance.LogError($"Failed to apply WPF theme: {ex.Message}");
        }
    }
}

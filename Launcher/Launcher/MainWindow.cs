using System;
using Eto.Forms;
using Eto.Drawing;
using Launcher.Forms;
using Launcher.Theming;
using Microsoft.Extensions.Logging;

namespace Launcher
{
	public sealed class MainWindow : Form
	{
		public static MainWindow Instance { get; private set; }

		public MainWindow()
		{
			Logger.LoggerInstance.LogInformation("Bootstrapping Launcher - MainWindow");

			Title = $"STAGA Launcher ({AppConfig.LauncherVersionString}) {(AppConfig.NetLatestVersion > AppConfig.LauncherVersion ? "(OUTDATED!)" : "(Latest)")}";

			ClientSize = new Size(800, 500);
			MinimumSize = new Size(600, 300);
			Content = new AuthControl();

			// Apply initial theme
			ApplyTheme(ThemeManager.Instance.CurrentTheme);

			// Subscribe to theme changes
			ThemeManager.Instance.ThemeChanged += OnThemeChanged;

			Instance = this;
			Logger.LoggerInstance.LogInformation("Bootstrapped Launcher - MainWindow");
		}

		private void OnThemeChanged(object? sender, ThemeConfiguration theme)
		{
			ApplyTheme(theme);
		}

		private void ApplyTheme(ThemeConfiguration theme)
		{
			try
			{
				BackgroundColor = theme.Background;
				Logger.LoggerInstance.LogInformation($"Applied {theme.Theme} theme to MainWindow");
			}
			catch (Exception ex)
			{
				Logger.LoggerInstance.LogError($"Failed to apply theme to MainWindow: {ex.Message}");
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing)
			{
				// Unsubscribe from theme changes
				ThemeManager.Instance.ThemeChanged -= OnThemeChanged;
			}
			base.Dispose(disposing);
		}
	}
}
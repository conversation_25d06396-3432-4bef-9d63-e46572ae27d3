using System;
using System.Runtime.InteropServices;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;
using Launcher.Theming;

namespace Launcher.Services;

/// <summary>
/// Windows-specific implementation of theme detection service
/// </summary>
public class WindowsThemeService : IThemeService, IDisposable
{
    private const string RegistryKeyPath = @"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize";
    private const string RegistryValueName = "AppsUseLightTheme";
    
    private bool _isMonitoring;
    private bool _disposed;

    public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

    /// <summary>
    /// Gets the current system theme
    /// </summary>
    public AppTheme GetSystemTheme()
    {
        return IsSystemDarkMode() ? AppTheme.Dark : AppTheme.Light;
    }

    /// <summary>
    /// Determines if the system is currently using dark mode
    /// </summary>
    public bool IsSystemDarkMode()
    {
        try
        {
            using var key = Registry.CurrentUser.OpenSubKey(RegistryKeyPath);
            if (key?.GetValue(RegistryValueName) is int value)
            {
                // Value of 0 means dark mode, 1 means light mode
                return value == 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LoggerInstance.LogWarning($"Failed to read theme from registry: {ex.Message}");
        }

        // Default to light mode if we can't determine the theme
        return false;
    }

    /// <summary>
    /// Starts monitoring for theme changes
    /// </summary>
    public void StartMonitoring()
    {
        if (_isMonitoring || _disposed)
            return;

        try
        {
            // Monitor registry changes for theme updates
            SystemEvents.UserPreferenceChanged += OnUserPreferenceChanged;
            _isMonitoring = true;
            
            Logger.LoggerInstance.LogInformation("Started monitoring system theme changes");
        }
        catch (Exception ex)
        {
            Logger.LoggerInstance.LogError($"Failed to start theme monitoring: {ex.Message}");
        }
    }

    /// <summary>
    /// Stops monitoring for theme changes
    /// </summary>
    public void StopMonitoring()
    {
        if (!_isMonitoring || _disposed)
            return;

        try
        {
            SystemEvents.UserPreferenceChanged -= OnUserPreferenceChanged;
            _isMonitoring = false;
            
            Logger.LoggerInstance.LogInformation("Stopped monitoring system theme changes");
        }
        catch (Exception ex)
        {
            Logger.LoggerInstance.LogError($"Failed to stop theme monitoring: {ex.Message}");
        }
    }

    private void OnUserPreferenceChanged(object sender, UserPreferenceChangedEventArgs e)
    {
        // Check if the change is related to general preferences (which includes theme)
        if (e.Category == UserPreferenceCategory.General)
        {
            var isDarkMode = IsSystemDarkMode();
            var theme = isDarkMode ? AppTheme.Dark : AppTheme.Light;
            
            Logger.LoggerInstance.LogInformation($"System theme changed to: {theme}");
            
            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(theme, isDarkMode));
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        StopMonitoring();
        _disposed = true;
    }
}

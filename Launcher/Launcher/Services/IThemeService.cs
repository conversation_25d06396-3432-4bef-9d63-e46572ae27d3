using System;
using Launcher.Theming;

namespace Launcher.Services;

/// <summary>
/// Service for detecting and monitoring system theme changes
/// </summary>
public interface IThemeService : IDisposable
{
    /// <summary>
    /// Gets the current system theme
    /// </summary>
    AppTheme GetSystemTheme();

    /// <summary>
    /// Determines if the system is currently using dark mode
    /// </summary>
    bool IsSystemDarkMode();

    /// <summary>
    /// Event raised when the system theme changes
    /// </summary>
    event EventHandler<ThemeChangedEventArgs> ThemeChanged;

    /// <summary>
    /// Starts monitoring for theme changes
    /// </summary>
    void StartMonitoring();

    /// <summary>
    /// Stops monitoring for theme changes
    /// </summary>
    void StopMonitoring();
}

/// <summary>
/// Event arguments for theme change notifications
/// </summary>
public class ThemeChangedEventArgs : EventArgs
{
    public AppTheme NewTheme { get; }
    public bool IsDarkMode { get; }

    public ThemeChangedEventArgs(AppTheme newTheme, bool isDarkMode)
    {
        NewTheme = newTheme;
        IsDarkMode = isDarkMode;
    }
}

using System;
using System.ComponentModel;
using Eto.Drawing;
using Eto.Forms;

namespace Launcher.Forms;

public class AuthControl : Panel
{
	public AuthControl()
	{
		Content = new TableLayout
		{
			Rows =
			{
				new TableRow { ScaleHeight = true }, // Empty row to push content to center
				new TableRow
				{
					Cells =
					{
						new TableCell
						{
							Control = new StackLayout
							{
								Items =
								{
									new Label
									{
										Text = "Contacting STAGANet",
										Font = new Font(SystemFont.Bold, 24),
										TextAlignment = TextAlignment.Center
									},
									new ProgressBar
									{
										Indeterminate = true,
										Width = 500
									},
								},
								Padding = new Padding(10),
								Spacing = 8,
								HorizontalContentAlignment = HorizontalAlignment.Center,
							},
							ScaleWidth = true
						}
					}
				},
				new TableRow { ScaleHeight = true } // Empty row to push content to center
			}
		};
		
		if(AppConfig.Token == null)
		{
			Application.Instance.AsyncInvoke(() =>
			{
				var topLevel = MainWindow.Instance; // finds the actual parent Form
				if (topLevel != null)
				{
					var form = new SigninForm();
					form.ShowModal(topLevel);
				}
			});
		}
	}
}
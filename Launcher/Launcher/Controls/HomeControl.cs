using System;
using System.Collections.Generic;
using Eto.Drawing;
using Eto.Forms;
using Launcher.Theming;
using Microsoft.Extensions.Logging;

namespace Launcher.Forms;

public class HomeControl : Panel
{
	private DynamicLayout _mainLayout;
	private Scrollable _newsfeed;
	private Label _testLabel;
	private List<Label> _newsLabels;

	public HomeControl()
	{
		InitializeComponents();
		ApplyTheme(ThemeManager.Instance.CurrentTheme);

		// Subscribe to theme changes
		ThemeManager.Instance.ThemeChanged += OnThemeChanged;
	}

	private void InitializeComponents()
	{
		_mainLayout = new DynamicLayout
		{
			Spacing = new Size(5, 5),
		};
		_mainLayout.BeginVertical();
		_mainLayout.BeginHorizontal();

		var newsItems = new[]
		{
			"test1",
			"test2",
			"test3",
			"test4",
			"test5",
			"test6",
			"test7",
			"test8",
			"test9",
			"test10"
		};

		// Create a layout for the newsfeed items
		var newsLayout = new DynamicLayout { Spacing = new Size(5, 5), Padding = new Padding(5) };
		_newsLabels = new List<Label>();

		foreach (var item in newsItems)
		{
			var label = new Label { Text = item, Wrap = WrapMode.Word };
			_newsLabels.Add(label);
			newsLayout.Add(label);
		}

		// Scrollable newsfeed container
		_newsfeed = new Scrollable
		{
			Width = 150,
			Content = newsLayout
		};

		_testLabel = new Label
		{
			Text = "test",
			Font = new Font(SystemFont.Bold, 24)
		};

		_mainLayout.Add(_newsfeed);
		_mainLayout.Add(_testLabel);

		_mainLayout.EndHorizontal();
		_mainLayout.EndVertical();

		Content = _mainLayout;
	}

	private void OnThemeChanged(object? sender, ThemeConfiguration theme)
	{
		ApplyTheme(theme);
	}

	private void ApplyTheme(ThemeConfiguration theme)
	{
		try
		{
			BackgroundColor = theme.Background;
			_newsfeed.BackgroundColor = theme.Surface;
			_testLabel.TextColor = theme.Text;

			// Apply theme to news labels
			foreach (var label in _newsLabels)
			{
				label.TextColor = theme.Text;
			}

			Logger.LoggerInstance.LogInformation($"Applied {theme.Theme} theme to HomeControl");
		}
		catch (Exception ex)
		{
			Logger.LoggerInstance.LogError($"Failed to apply theme to HomeControl: {ex.Message}");
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing)
		{
			ThemeManager.Instance.ThemeChanged -= OnThemeChanged;
		}
		base.Dispose(disposing);
	}
}
using System.Globalization;
using Launcher.Theming;

namespace Launcher;

public struct AppConfig
{
	public static string BaseUrl = "http://localhost";
	public static string Token;

	public static double NetLatestVersion;
	public static string NetLatestVersionString => NetLatestVersion.ToString(CultureInfo.InvariantCulture);

	public const double LauncherVersion = 0.01;
	public static string LauncherVersionString => LauncherVersion.ToString(CultureInfo.InvariantCulture);

	/// <summary>
	/// User's preferred theme setting. Defaults to System (follows OS theme)
	/// </summary>
	public static AppTheme PreferredTheme { get; set; } = AppTheme.System;
}
using Eto.Drawing;
using Eto.Forms;

namespace Launcher.Forms;

public class SigninForm : Dialog
{
	public SigninForm()
	{
		Title = "Sign into STAGANet";
		ClientSize = new Size(500, 185);
		Resizable = false;
		Content = new StackLayout
		{
			Items =
			{
				new Label { Text = "This game requires an online account to play.", TextAlignment = TextAlignment.Center, Font = new Font(SystemFont.Bold, 16)},
				new Label { Text = "Sign in with your account below." },
				new TextBox { PlaceholderText = "Username" },
				new PasswordBox(),
				new CheckBox {Text = "Remember session"},
				new Button { Text = "Sign in" }
			},
			Padding = new Padding(10),
			Spacing = 8,
			HorizontalContentAlignment = HorizontalAlignment.Stretch,
			VerticalContentAlignment = VerticalAlignment.Center
		};
		
		ClientSize = (Size)GetPreferredSize();
	}
}
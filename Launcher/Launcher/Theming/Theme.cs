using Eto.Drawing;

namespace Launcher.Theming;

/// <summary>
/// Represents the available application themes
/// </summary>
public enum AppTheme
{
    Light,
    Dark,
    System
}

/// <summary>
/// Contains color definitions for application themes
/// </summary>
public static class ThemeColors
{
    public static class Light
    {
        public static readonly Color Background = Color.FromRgb(0xFFFFFF);
        public static readonly Color Surface = Color.FromRgb(0xF5F5F5);
        public static readonly Color Primary = Color.FromRgb(0x0078D4);
        public static readonly Color Secondary = Color.FromRgb(0x6B6B6B);
        public static readonly Color Text = Color.FromRgb(0x000000);
        public static readonly Color TextSecondary = Color.FromRgb(0x424242);
        public static readonly Color Border = Color.FromRgb(0xD1D1D1);
        public static readonly Color Accent = Color.FromRgb(0x0078D4);
        public static readonly Color Error = Color.FromRgb(0xD32F2F);
        public static readonly Color Success = Color.FromRgb(0x388E3C);
        public static readonly Color Warning = Color.FromRgb(0xF57C00);
    }

    public static class Dark
    {
        public static readonly Color Background = Color.FromRgb(0x1E1E1E);
        public static readonly Color Surface = Color.FromRgb(0x2D2D30);
        public static readonly Color Primary = Color.FromRgb(0x0E639C);
        public static readonly Color Secondary = Color.FromRgb(0x9A9A9A);
        public static readonly Color Text = Color.FromRgb(0xFFFFFF);
        public static readonly Color TextSecondary = Color.FromRgb(0xCCCCCC);
        public static readonly Color Border = Color.FromRgb(0x3F3F46);
        public static readonly Color Accent = Color.FromRgb(0x0E639C);
        public static readonly Color Error = Color.FromRgb(0xF87171);
        public static readonly Color Success = Color.FromRgb(0x4ADE80);
        public static readonly Color Warning = Color.FromRgb(0xFBBF24);
    }
}

/// <summary>
/// Represents a complete theme configuration
/// </summary>
public class ThemeConfiguration
{
    public AppTheme Theme { get; set; }
    public Color Background { get; set; }
    public Color Surface { get; set; }
    public Color Primary { get; set; }
    public Color Secondary { get; set; }
    public Color Text { get; set; }
    public Color TextSecondary { get; set; }
    public Color Border { get; set; }
    public Color Accent { get; set; }
    public Color Error { get; set; }
    public Color Success { get; set; }
    public Color Warning { get; set; }

    public static ThemeConfiguration GetLightTheme() => new()
    {
        Theme = AppTheme.Light,
        Background = ThemeColors.Light.Background,
        Surface = ThemeColors.Light.Surface,
        Primary = ThemeColors.Light.Primary,
        Secondary = ThemeColors.Light.Secondary,
        Text = ThemeColors.Light.Text,
        TextSecondary = ThemeColors.Light.TextSecondary,
        Border = ThemeColors.Light.Border,
        Accent = ThemeColors.Light.Accent,
        Error = ThemeColors.Light.Error,
        Success = ThemeColors.Light.Success,
        Warning = ThemeColors.Light.Warning
    };

    public static ThemeConfiguration GetDarkTheme() => new()
    {
        Theme = AppTheme.Dark,
        Background = ThemeColors.Dark.Background,
        Surface = ThemeColors.Dark.Surface,
        Primary = ThemeColors.Dark.Primary,
        Secondary = ThemeColors.Dark.Secondary,
        Text = ThemeColors.Dark.Text,
        TextSecondary = ThemeColors.Dark.TextSecondary,
        Border = ThemeColors.Dark.Border,
        Accent = ThemeColors.Dark.Accent,
        Error = ThemeColors.Dark.Error,
        Success = ThemeColors.Dark.Success,
        Warning = ThemeColors.Dark.Warning
    };
}

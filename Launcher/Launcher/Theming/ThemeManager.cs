using System;
using Eto.Forms;
using Eto.Drawing;
using Microsoft.Extensions.Logging;
using Launcher.Services;

namespace Launcher.Theming;

/// <summary>
/// Central manager for application theming
/// </summary>
public class ThemeManager : IDisposable
{
    private static ThemeManager? _instance;
    private readonly IThemeService _themeService;
    private ThemeConfiguration _currentTheme;
    private AppTheme _preferredTheme = AppTheme.System;
    private bool _disposed;

    public static ThemeManager Instance => _instance ??= new ThemeManager();

    /// <summary>
    /// Event raised when the active theme changes
    /// </summary>
    public event EventHandler<ThemeConfiguration>? ThemeChanged;

    /// <summary>
    /// Gets the current active theme configuration
    /// </summary>
    public ThemeConfiguration CurrentTheme => _currentTheme;

    /// <summary>
    /// Gets or sets the preferred theme setting
    /// </summary>
    public AppTheme PreferredTheme
    {
        get => _preferredTheme;
        set
        {
            if (_preferredTheme != value)
            {
                _preferredTheme = value;
                UpdateTheme();
            }
        }
    }

    private ThemeManager()
    {
        _themeService = new WindowsThemeService();
        _currentTheme = GetEffectiveTheme();
        
        // Subscribe to system theme changes
        _themeService.ThemeChanged += OnSystemThemeChanged;
        _themeService.StartMonitoring();
        
        Logger.LoggerInstance.LogInformation($"ThemeManager initialized with theme: {_currentTheme.Theme}");
    }

    /// <summary>
    /// Initializes the theme manager and applies the initial theme
    /// </summary>
    public void Initialize()
    {
        ApplyTheme(_currentTheme);
        Logger.LoggerInstance.LogInformation("ThemeManager initialized and theme applied");
    }

    /// <summary>
    /// Gets the effective theme based on the preferred setting
    /// </summary>
    private ThemeConfiguration GetEffectiveTheme()
    {
        return _preferredTheme switch
        {
            AppTheme.Light => ThemeConfiguration.GetLightTheme(),
            AppTheme.Dark => ThemeConfiguration.GetDarkTheme(),
            AppTheme.System => _themeService.IsSystemDarkMode() 
                ? ThemeConfiguration.GetDarkTheme() 
                : ThemeConfiguration.GetLightTheme(),
            _ => ThemeConfiguration.GetLightTheme()
        };
    }

    /// <summary>
    /// Updates the current theme based on preferences and system settings
    /// </summary>
    private void UpdateTheme()
    {
        var newTheme = GetEffectiveTheme();
        if (newTheme.Theme != _currentTheme.Theme)
        {
            _currentTheme = newTheme;
            ApplyTheme(_currentTheme);
            ThemeChanged?.Invoke(this, _currentTheme);
            
            Logger.LoggerInstance.LogInformation($"Theme updated to: {_currentTheme.Theme}");
        }
    }

    /// <summary>
    /// Applies the specified theme to the application
    /// </summary>
    private void ApplyTheme(ThemeConfiguration theme)
    {
        try
        {
            // Apply Eto.Forms styling
            ApplyEtoStyling(theme);
            
            Logger.LoggerInstance.LogInformation($"Applied {theme.Theme} theme to application");
        }
        catch (Exception ex)
        {
            Logger.LoggerInstance.LogError($"Failed to apply theme: {ex.Message}");
        }
    }

    /// <summary>
    /// Applies theme styling to Eto.Forms controls
    /// </summary>
    private void ApplyEtoStyling(ThemeConfiguration theme)
    {
        // Apply global styles using Eto.Style
        Eto.Style.Add<Eto.Forms.Form>(null, control =>
        {
            control.BackgroundColor = theme.Background;
        });

        Eto.Style.Add<Eto.Forms.Panel>(null, control =>
        {
            control.BackgroundColor = theme.Background;
        });

        Eto.Style.Add<Eto.Forms.Label>(null, control =>
        {
            control.TextColor = theme.Text;
        });

        Eto.Style.Add<Eto.Forms.Button>(null, control =>
        {
            control.BackgroundColor = theme.Surface;
            control.TextColor = theme.Text;
        });

        Eto.Style.Add<Eto.Forms.TextBox>(null, control =>
        {
            control.BackgroundColor = theme.Surface;
            control.TextColor = theme.Text;
        });

        Eto.Style.Add<Eto.Forms.Dialog>(null, control =>
        {
            control.BackgroundColor = theme.Background;
        });
    }

    /// <summary>
    /// Handles system theme changes
    /// </summary>
    private void OnSystemThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        if (_preferredTheme == AppTheme.System)
        {
            UpdateTheme();
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _themeService?.StopMonitoring();
        _themeService?.Dispose();
        _disposed = true;
    }
}
